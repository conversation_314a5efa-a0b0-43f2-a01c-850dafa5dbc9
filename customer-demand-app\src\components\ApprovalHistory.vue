<template>
  <div class="approval-history">
    <div class="history-timeline">
      <el-timeline>
        <el-timeline-item
          v-for="record in approvalRecords"
          :key="record.id"
          :timestamp="record.timestamp"
          :type="getTimelineType(record.result)"
          :icon="getTimelineIcon(record.result)"
          placement="top"
        >
          <el-card>
            <div class="record-header">
              <div class="record-title">
                <span class="step-name">{{ record.stepName }}</span>
                <el-tag :type="getResultType(record.result)" size="small">
                  {{ record.result }}
                </el-tag>
              </div>
              <div class="record-meta">
                <span class="handler">{{ record.handler }}</span>
                <span class="department">{{ record.department }}</span>
              </div>
            </div>
            
            <div class="record-content">
              <div class="opinion-section" v-if="record.opinion">
                <h5>处理意见：</h5>
                <p class="opinion-text">{{ record.opinion }}</p>
              </div>
              
              <div class="attachments" v-if="record.attachments && record.attachments.length > 0">
                <h5>相关附件：</h5>
                <div class="attachment-list">
                  <el-tag 
                    v-for="attachment in record.attachments" 
                    :key="attachment.id"
                    type="info" 
                    size="small"
                    class="attachment-tag"
                  >
                    <el-icon><Paperclip /></el-icon>
                    {{ attachment.name }}
                  </el-tag>
                </div>
              </div>
              
              <div class="duration-info" v-if="record.duration">
                <el-icon><Clock /></el-icon>
                <span>处理用时：{{ record.duration }}</span>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 统计信息 -->
    <div class="statistics">
      <el-card>
        <template #header>
          <span>审批统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.totalSteps }}</div>
              <div class="stat-label">总步骤数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.approvedSteps }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.rejectedSteps }}</div>
              <div class="stat-label">已驳回</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.totalDuration }}</div>
              <div class="stat-label">总用时</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CircleCheck, CircleClose, Clock, Paperclip, Warning } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  csrId: string
}>()

// 示例审批记录数据
const approvalRecords = ref([
  {
    id: 1,
    stepName: '提交申请',
    handler: '张三',
    department: '研发工程部',
    timestamp: '2024-01-10 09:00:00',
    result: '提交',
    opinion: '提交CSR申请，请相关部门审批',
    attachments: [
      { id: 1, name: '一汽大众采购通则.pdf' },
      { id: 2, name: '技术规范说明.docx' }
    ],
    duration: null
  },
  {
    id: 2,
    stepName: '部门经理初审',
    handler: '李四',
    department: '研发工程部',
    timestamp: '2024-01-10 14:30:00',
    result: '同意',
    opinion: '技术要求明确，符合公司标准，同意进入下一步审批',
    attachments: [],
    duration: '5小时30分钟'
  },
  {
    id: 3,
    stepName: '技术总监审批',
    handler: '王五',
    department: '技术部',
    timestamp: '2024-01-11 10:15:00',
    result: '同意',
    opinion: '技术方案可行，建议加强质量控制措施',
    attachments: [
      { id: 3, name: '技术评审报告.pdf' }
    ],
    duration: '19小时45分钟'
  },
  {
    id: 4,
    stepName: '品管部会签',
    handler: '赵六',
    department: '品管部',
    timestamp: '2024-01-11 16:20:00',
    result: '同意',
    opinion: '质量要求符合IATF16949标准，同意实施',
    attachments: [],
    duration: '6小时5分钟'
  },
  {
    id: 5,
    stepName: '生产部会签',
    handler: '孙七',
    department: '生产部',
    timestamp: '2024-01-12 09:45:00',
    result: '同意',
    opinion: '生产工艺可以满足要求，需要增加设备投入',
    attachments: [
      { id: 4, name: '生产可行性分析.xlsx' }
    ],
    duration: '17小时25分钟'
  },
  {
    id: 6,
    stepName: '采购部会签',
    handler: '周八',
    department: '采购部',
    timestamp: '2024-01-12 14:10:00',
    result: '同意',
    opinion: '供应商资源充足，成本在可接受范围内',
    attachments: [],
    duration: '4小时25分钟'
  },
  {
    id: 7,
    stepName: '财务部会签',
    handler: '吴九',
    department: '财务部',
    timestamp: '2024-01-13 11:30:00',
    result: '同意',
    opinion: '成本预算合理，对财务影响可控',
    attachments: [
      { id: 5, name: '成本影响分析.xlsx' }
    ],
    duration: '21小时20分钟'
  },
  {
    id: 8,
    stepName: '最终发布',
    handler: '系统自动',
    department: '系统',
    timestamp: '2024-01-15 08:00:00',
    result: '发布',
    opinion: '所有审批会签完成，CSR正式发布生效',
    attachments: [],
    duration: '自动处理'
  }
])

// 统计信息
const statistics = computed(() => {
  const total = approvalRecords.value.length
  const approved = approvalRecords.value.filter(r => r.result === '同意' || r.result === '发布').length
  const rejected = approvalRecords.value.filter(r => r.result === '驳回').length
  
  // 计算总用时（从提交到发布）
  const startTime = new Date(approvalRecords.value[0]?.timestamp)
  const endTime = new Date(approvalRecords.value[approvalRecords.value.length - 1]?.timestamp)
  const totalHours = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60))
  const days = Math.floor(totalHours / 24)
  const hours = totalHours % 24
  const totalDuration = `${days}天${hours}小时`
  
  return {
    totalSteps: total,
    approvedSteps: approved,
    rejectedSteps: rejected,
    totalDuration
  }
})

// 方法
const getTimelineType = (result: string) => {
  const typeMap = {
    '提交': 'primary',
    '同意': 'success',
    '驳回': 'danger',
    '发布': 'success',
    '待处理': 'warning'
  }
  return typeMap[result] || 'primary'
}

const getTimelineIcon = (result: string) => {
  const iconMap = {
    '同意': CircleCheck,
    '驳回': CircleClose,
    '发布': CircleCheck,
    '待处理': Warning
  }
  return iconMap[result] || null
}

const getResultType = (result: string) => {
  const typeMap = {
    '提交': 'primary',
    '同意': 'success',
    '驳回': 'danger',
    '发布': 'success',
    '待处理': 'warning'
  }
  return typeMap[result] || ''
}

onMounted(() => {
  // 根据csrId加载审批历史数据
  console.log('Loading approval history for CSR:', props.csrId)
})
</script>

<style scoped>
.approval-history {
  width: 100%;
}

.history-timeline {
  margin-bottom: 30px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.record-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-name {
  font-weight: bold;
  color: #303133;
  font-size: 16px;
}

.record-meta {
  text-align: right;
  color: #909399;
  font-size: 14px;
}

.handler {
  display: block;
  font-weight: 500;
  color: #606266;
}

.department {
  font-size: 12px;
}

.record-content {
  color: #606266;
}

.opinion-section {
  margin-bottom: 16px;
}

.opinion-section h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.opinion-text {
  margin: 0;
  line-height: 1.6;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.attachments {
  margin-bottom: 16px;
}

.attachments h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-tag {
  cursor: pointer;
}

.attachment-tag:hover {
  background-color: #409EFF;
  color: white;
}

.duration-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 12px;
}

.statistics {
  margin-top: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}
</style>
