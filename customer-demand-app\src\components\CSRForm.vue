<template>
  <div class="csr-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="formData.customerName" placeholder="请输入客户名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件名称" prop="fileName">
            <el-input v-model="formData.fileName" placeholder="请输入文件名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="要求内容" prop="requirementContent">
        <el-input
          v-model="formData.requirementContent"
          type="textarea"
          :rows="4"
          placeholder="请输入具体的特殊要求描述"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对应过程/条款" prop="processClause">
            <el-input v-model="formData.processClause" placeholder="如：IATF16949-8.3设计开发" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责部门" prop="department">
            <el-select v-model="formData.department" placeholder="请选择负责部门" style="width: 100%">
              <el-option label="研发工程部" value="研发工程部" />
              <el-option label="生产部" value="生产部" />
              <el-option label="品管部" value="品管部" />
              <el-option label="采购部" value="采购部" />
              <el-option label="销售部" value="销售部" />
              <el-option label="财务部" value="财务部" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="控制方法" prop="controlMethod">
        <el-input
          v-model="formData.controlMethod"
          type="textarea"
          :rows="3"
          placeholder="请输入工厂内部将如何满足和控制此特殊要求的方法或措施"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="文件编号" prop="fileNumber">
            <el-input v-model="formData.fileNumber" placeholder="客户文件编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发行人" prop="publisher">
            <el-input v-model="formData.publisher" placeholder="文件发布方" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版本" prop="version">
            <el-input v-model="formData.version" placeholder="如：V1.0" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布日期" prop="publishDate">
            <el-date-picker
              v-model="formData.publishDate"
              type="date"
              placeholder="选择发布日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附件上传">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              :limit="5"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
            >
              <el-button size="small" type="primary">
                <el-icon><Upload /></el-icon>
                选择文件
              </el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持pdf、word、excel格式，最多5个文件
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSaveDraft">保存草稿</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  csrData?: any
}>()

// Emits
const emit = defineEmits<{
  save: [data: any]
  cancel: []
}>()

// 表单引用
const formRef = ref()

// 文件列表
const fileList = ref([])

// 表单数据
const formData = reactive({
  customerName: '',
  fileName: '',
  requirementContent: '',
  processClause: '',
  department: '',
  controlMethod: '',
  fileNumber: '',
  publisher: '',
  version: '',
  publishDate: ''
})

// 表单验证规则
const rules = {
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' }
  ],
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ],
  requirementContent: [
    { required: true, message: '请输入要求内容', trigger: 'blur' }
  ],
  processClause: [
    { required: true, message: '请输入对应过程/条款', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择负责部门', trigger: 'change' }
  ],
  controlMethod: [
    { required: true, message: '请输入控制方法', trigger: 'blur' }
  ],
  fileNumber: [
    { required: true, message: '请输入文件编号', trigger: 'blur' }
  ],
  publisher: [
    { required: true, message: '请输入发行人', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本', trigger: 'blur' }
  ],
  publishDate: [
    { required: true, message: '请选择发布日期', trigger: 'change' }
  ]
}

// 监听props变化，初始化表单数据
watch(() => props.csrData, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true })

// 文件上传处理
const handleFileChange = (file: any, fileList: any[]) => {
  // 这里可以处理文件上传逻辑
}

// 保存草稿
const handleSaveDraft = () => {
  const data = {
    ...formData,
    status: '草稿'
  }
  emit('save', data)
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()
    const data = {
      ...formData,
      status: props.csrData?.status || '草稿'
    }
    emit('save', data)
  } catch (error) {
    ElMessage.error('请完善必填信息')
  }
}

// 取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.csr-form {
  padding: 20px 0;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

.upload-demo {
  width: 100%;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style>
