<template>
  <div class="process-tracker">
    <!-- CSR基本信息 -->
    <div class="csr-basic-info">
      <h3>CSR基本信息</h3>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="CSR编号">{{ csrInfo.csrId }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ csrInfo.customerName }}</el-descriptions-item>
        <el-descriptions-item label="文件名称">{{ csrInfo.fileName }}</el-descriptions-item>
        <el-descriptions-item label="负责部门">{{ csrInfo.department }}</el-descriptions-item>
        <el-descriptions-item label="提交人">{{ csrInfo.submitter }}</el-descriptions-item>
        <el-descriptions-item label="当前状态">
          <el-tag :type="getStatusType(csrInfo.status)">{{ csrInfo.status }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 流程进度 -->
    <div class="process-progress">
      <h3>流程进度</h3>
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="提交申请" :description="getStepDescription(0)" />
        <el-step title="部门审批" :description="getStepDescription(1)" />
        <el-step title="多部门会签" :description="getStepDescription(2)" />
        <el-step title="发布生效" :description="getStepDescription(3)" />
      </el-steps>
    </div>

    <!-- 审批会签记录 -->
    <div class="approval-records">
      <h3>审批会签记录</h3>
      <el-timeline>
        <el-timeline-item
          v-for="record in approvalRecords"
          :key="record.id"
          :timestamp="record.timestamp"
          :type="getTimelineType(record.result)"
          :icon="getTimelineIcon(record.result)"
        >
          <el-card>
            <div class="record-header">
              <span class="record-title">{{ record.title }}</span>
              <el-tag :type="getResultType(record.result)" size="small">
                {{ record.result }}
              </el-tag>
            </div>
            <div class="record-content">
              <p><strong>处理人：</strong>{{ record.handler }}</p>
              <p><strong>处理部门：</strong>{{ record.department }}</p>
              <p v-if="record.opinion"><strong>处理意见：</strong>{{ record.opinion }}</p>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 会签部门状态 -->
    <div class="countersign-status" v-if="countersignDepartments.length > 0">
      <h3>会签部门状态</h3>
      <el-row :gutter="20">
        <el-col :span="8" v-for="dept in countersignDepartments" :key="dept.name">
          <el-card class="dept-card">
            <div class="dept-info">
              <div class="dept-name">{{ dept.name }}</div>
              <div class="dept-status">
                <el-tag :type="getDeptStatusType(dept.status)">
                  {{ dept.status }}
                </el-tag>
              </div>
            </div>
            <div class="dept-details">
              <p><strong>负责人：</strong>{{ dept.handler || '待指定' }}</p>
              <p><strong>处理时间：</strong>{{ dept.handleTime || '待处理' }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CircleCheck, CircleClose, Clock } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  csrId: string
}>()

// 响应式数据
const csrInfo = ref({
  csrId: 'CSR-2024-007',
  customerName: '蔚来汽车',
  fileName: '蔚来汽车智能化要求',
  department: '研发工程部',
  submitter: '张三',
  status: '会签中'
})

const currentStep = ref(2) // 当前步骤索引

const approvalRecords = ref([
  {
    id: 1,
    title: '提交CSR申请',
    handler: '张三',
    department: '研发工程部',
    timestamp: '2024-06-15 09:00:00',
    result: '提交',
    opinion: '提交CSR申请，请相关部门审批'
  },
  {
    id: 2,
    title: '部门经理审批',
    handler: '李四',
    department: '研发工程部',
    timestamp: '2024-06-15 14:30:00',
    result: '同意',
    opinion: '技术要求合理，同意进入会签流程'
  },
  {
    id: 3,
    title: '技术总监审批',
    handler: '王五',
    department: '技术部',
    timestamp: '2024-06-16 10:15:00',
    result: '同意',
    opinion: '技术方案可行，同意实施'
  },
  {
    id: 4,
    title: '品管部会签',
    handler: '赵六',
    department: '品管部',
    timestamp: '2024-06-16 16:20:00',
    result: '同意',
    opinion: '质量要求明确，可以执行'
  }
])

const countersignDepartments = ref([
  {
    name: '生产部',
    handler: '孙七',
    status: '待会签',
    handleTime: null
  },
  {
    name: '采购部',
    handler: '周八',
    status: '待会签',
    handleTime: null
  },
  {
    name: '财务部',
    handler: '吴九',
    status: '待会签',
    handleTime: null
  }
])

// 计算属性和方法
const getStepDescription = (stepIndex: number) => {
  const descriptions = [
    '2024-06-15 09:00:00',
    '2024-06-16 10:15:00',
    '进行中...',
    '待完成'
  ]
  return descriptions[stepIndex] || ''
}

const getStatusType = (status: string) => {
  const statusMap = {
    '草稿': '',
    '待审批': 'warning',
    '审批中': 'warning',
    '会签中': 'info',
    '已发布': 'success',
    '已废止': 'danger'
  }
  return statusMap[status] || ''
}

const getTimelineType = (result: string) => {
  const typeMap = {
    '提交': 'primary',
    '同意': 'success',
    '驳回': 'danger',
    '待处理': 'info'
  }
  return typeMap[result] || 'primary'
}

const getTimelineIcon = (result: string) => {
  const iconMap = {
    '同意': CircleCheck,
    '驳回': CircleClose,
    '待处理': Clock
  }
  return iconMap[result] || null
}

const getResultType = (result: string) => {
  const typeMap = {
    '提交': 'primary',
    '同意': 'success',
    '驳回': 'danger',
    '待处理': 'warning'
  }
  return typeMap[result] || ''
}

const getDeptStatusType = (status: string) => {
  const typeMap = {
    '已会签': 'success',
    '待会签': 'warning',
    '已驳回': 'danger'
  }
  return typeMap[status] || ''
}

onMounted(() => {
  // 根据csrId加载具体数据
  console.log('Loading process data for CSR:', props.csrId)
})
</script>

<style scoped>
.process-tracker {
  width: 100%;
}

.csr-basic-info,
.process-progress,
.approval-records,
.countersign-status {
  margin-bottom: 30px;
}

.csr-basic-info h3,
.process-progress h3,
.approval-records h3,
.countersign-status h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.process-progress {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-title {
  font-weight: bold;
  color: #303133;
}

.record-content p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.dept-card {
  margin-bottom: 16px;
}

.dept-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.dept-name {
  font-weight: bold;
  color: #303133;
}

.dept-details p {
  margin: 6px 0;
  color: #606266;
  font-size: 14px;
}
</style>
