<template>
  <div class="query-trace">
    <div class="page-header">
      <h1>查询与追溯</h1>
      <p>CSR查询、历史版本追溯和审批记录查看</p>
    </div>

    <!-- 高级查询 -->
    <div class="advanced-search">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>高级查询</span>
            <el-button 
              type="text" 
              @click="toggleSearchForm"
              :icon="showSearchForm ? 'ArrowUp' : 'ArrowDown'"
            >
              {{ showSearchForm ? '收起' : '展开' }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="showSearchForm">
            <el-form :model="searchForm" :inline="true" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="CSR编号">
                    <el-input v-model="searchForm.csrId" placeholder="请输入CSR编号" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="客户名称">
                    <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="文件名称">
                    <el-input v-model="searchForm.fileName" placeholder="请输入文件名称" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="负责部门">
                    <el-select v-model="searchForm.department" placeholder="请选择" clearable style="width: 100%">
                      <el-option label="研发工程部" value="研发工程部" />
                      <el-option label="生产部" value="生产部" />
                      <el-option label="品管部" value="品管部" />
                      <el-option label="采购部" value="采购部" />
                      <el-option label="销售部" value="销售部" />
                      <el-option label="财务部" value="财务部" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="状态">
                    <el-select v-model="searchForm.status" placeholder="请选择" clearable style="width: 100%">
                      <el-option label="草稿" value="草稿" />
                      <el-option label="待审批" value="待审批" />
                      <el-option label="审批中" value="审批中" />
                      <el-option label="会签中" value="会签中" />
                      <el-option label="已发布" value="已发布" />
                      <el-option label="已废止" value="已废止" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="版本">
                    <el-input v-model="searchForm.version" placeholder="请输入版本号" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="发布日期">
                    <el-date-picker
                      v-model="searchForm.publishDateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发行人">
                    <el-input v-model="searchForm.publisher" placeholder="请输入发行人" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="关键词">
                    <el-input v-model="searchForm.keyword" placeholder="搜索要求内容关键词" clearable />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row>
                <el-col :span="24">
                  <el-form-item>
                    <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
                    <el-button @click="handleReset" :icon="Refresh">重置</el-button>
                    <el-button type="success" @click="handleExport" :icon="Download">导出结果</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- 查询结果 -->
    <div class="search-results">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>查询结果 (共 {{ totalCount }} 条)</span>
            <div class="header-actions">
              <el-button-group>
                <el-button 
                  :type="viewMode === 'table' ? 'primary' : ''" 
                  @click="viewMode = 'table'"
                  :icon="Grid"
                  size="small"
                >
                  表格视图
                </el-button>
                <el-button 
                  :type="viewMode === 'card' ? 'primary' : ''" 
                  @click="viewMode = 'card'"
                  :icon="Postcard"
                  size="small"
                >
                  卡片视图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <el-table :data="searchResults" style="width: 100%" stripe>
            <el-table-column prop="csrId" label="CSR编号" width="120" />
            <el-table-column prop="customerName" label="客户名称" width="150" />
            <el-table-column prop="fileName" label="文件名称" min-width="200" show-overflow-tooltip />
            <el-table-column prop="department" label="负责部门" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="publishDate" label="发布日期" width="120" />
            <el-table-column prop="publisher" label="发行人" width="120" />
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="viewDetail(scope.row)">查看详情</el-button>
                  <el-button size="small" type="info" @click="viewVersions(scope.row)">版本历史</el-button>
                  <el-button size="small" type="warning" @click="viewApprovalHistory(scope.row)">审批记录</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="20">
            <el-col :span="8" v-for="item in searchResults" :key="item.id">
              <el-card class="csr-card" shadow="hover">
                <template #header>
                  <div class="csr-card-header">
                    <span class="csr-id">{{ item.csrId }}</span>
                    <el-tag :type="getStatusType(item.status)" size="small">
                      {{ item.status }}
                    </el-tag>
                  </div>
                </template>
                
                <div class="csr-card-content">
                  <h4>{{ item.fileName }}</h4>
                  <p><strong>客户：</strong>{{ item.customerName }}</p>
                  <p><strong>部门：</strong>{{ item.department }}</p>
                  <p><strong>版本：</strong>{{ item.version }}</p>
                  <p><strong>发布：</strong>{{ item.publishDate }}</p>
                </div>
                
                <div class="csr-card-actions">
                  <el-button size="small" @click="viewDetail(item)">详情</el-button>
                  <el-button size="small" type="info" @click="viewVersions(item)">历史</el-button>
                  <el-button size="small" type="warning" @click="viewApprovalHistory(item)">记录</el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48]"
            :total="totalCount"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 版本历史对话框 -->
    <el-dialog v-model="showVersionDialog" title="版本历史" width="900px">
      <VersionHistory :csr-id="selectedCSRId" />
    </el-dialog>

    <!-- 审批记录对话框 -->
    <el-dialog v-model="showApprovalDialog" title="审批记录" width="900px">
      <ApprovalHistory :csr-id="selectedCSRId" />
    </el-dialog>

    <!-- CSR详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="CSR详情" width="1000px">
      <CSRDetail :csr-data="selectedCSR" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Download, 
  Grid, 
  Postcard,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import VersionHistory from '../components/VersionHistory.vue'
import ApprovalHistory from '../components/ApprovalHistory.vue'
import CSRDetail from '../components/CSRDetail.vue'

// 响应式数据
const showSearchForm = ref(true)
const viewMode = ref('table') // 'table' | 'card'
const currentPage = ref(1)
const pageSize = ref(12)
const totalCount = ref(0)
const showVersionDialog = ref(false)
const showApprovalDialog = ref(false)
const showDetailDialog = ref(false)
const selectedCSRId = ref('')
const selectedCSR = ref(null)

// 搜索表单
const searchForm = reactive({
  csrId: '',
  customerName: '',
  fileName: '',
  department: '',
  status: '',
  version: '',
  publishDateRange: null,
  publisher: '',
  keyword: ''
})

// 示例数据
const allData = ref([
  {
    id: 1,
    csrId: 'CSR-2024-001',
    customerName: '一汽大众',
    fileName: '一汽大众采购通则V2.1',
    requirementContent: '供应商必须通过IATF16949认证，产品质量必须符合VDA6.3标准...',
    processClause: 'IATF16949-8.3设计开发',
    department: '品管部',
    controlMethod: '建立供应商评审流程，定期审核认证状态',
    fileNumber: 'VW-PUR-2024-001',
    publisher: '一汽大众采购部',
    version: 'V2.1',
    publishDate: '2024-01-15',
    status: '已发布'
  },
  {
    id: 2,
    csrId: 'CSR-2024-002',
    customerName: '上汽通用',
    fileName: '上汽通用质量协议',
    requirementContent: '零部件PPM要求小于50，首次通过率大于98%...',
    processClause: 'IATF16949-8.5生产',
    department: '生产部',
    controlMethod: '建立SPC统计过程控制，实时监控产品质量',
    fileNumber: 'GM-QUA-2024-002',
    publisher: '上汽通用质量部',
    version: 'V1.0',
    publishDate: '2024-02-20',
    status: '会签中'
  },
  {
    id: 3,
    csrId: 'CSR-2024-003',
    customerName: '比亚迪',
    fileName: '比亚迪环保要求规范',
    requirementContent: '产品材料必须符合RoHS标准，包装材料100%可回收...',
    processClause: 'IATF16949-8.1运行策划',
    department: '研发工程部',
    controlMethod: '建立绿色供应链管理体系，定期检测有害物质',
    fileNumber: 'BYD-ENV-2024-003',
    publisher: '比亚迪采购部',
    version: 'V1.2',
    publishDate: '2024-03-10',
    status: '审批中'
  },
  {
    id: 4,
    csrId: 'CSR-2024-004',
    customerName: '长城汽车',
    fileName: '长城汽车供应商管理要求',
    requirementContent: '供应商必须建立完善的风险管理体系，确保供应链稳定...',
    processClause: 'IATF16949-8.4外部提供过程',
    department: '采购部',
    controlMethod: '建立供应商风险评估模型，定期评估供应商风险等级',
    fileNumber: 'GWM-SUP-2024-004',
    publisher: '长城汽车采购部',
    version: 'V3.0',
    publishDate: '2024-04-05',
    status: '待审批'
  },
  {
    id: 5,
    csrId: 'CSR-2024-005',
    customerName: '吉利汽车',
    fileName: '吉利汽车成本控制要求',
    requirementContent: '年度降本目标5%，通过技术改进和工艺优化实现...',
    processClause: 'IATF16949-9.1监视测量',
    department: '财务部',
    controlMethod: '建立成本分析模型，月度跟踪成本变化趋势',
    fileNumber: 'GEELY-COST-2024-005',
    publisher: '吉利汽车财务部',
    version: 'V1.0',
    publishDate: '2024-05-12',
    status: '草稿'
  }
])

// 计算属性
const searchResults = computed(() => {
  let data = allData.value
  
  // 应用搜索条件
  if (searchForm.csrId) {
    data = data.filter(item => item.csrId.includes(searchForm.csrId))
  }
  if (searchForm.customerName) {
    data = data.filter(item => item.customerName.includes(searchForm.customerName))
  }
  if (searchForm.fileName) {
    data = data.filter(item => item.fileName.includes(searchForm.fileName))
  }
  if (searchForm.department) {
    data = data.filter(item => item.department === searchForm.department)
  }
  if (searchForm.status) {
    data = data.filter(item => item.status === searchForm.status)
  }
  if (searchForm.version) {
    data = data.filter(item => item.version.includes(searchForm.version))
  }
  if (searchForm.publisher) {
    data = data.filter(item => item.publisher.includes(searchForm.publisher))
  }
  if (searchForm.keyword) {
    data = data.filter(item => item.requirementContent.includes(searchForm.keyword))
  }
  
  totalCount.value = data.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return data.slice(start, end)
})

// 方法
const toggleSearchForm = () => {
  showSearchForm.value = !showSearchForm.value
}

const getStatusType = (status: string) => {
  const statusMap = {
    '草稿': '',
    '待审批': 'warning',
    '审批中': 'warning',
    '会签中': 'info',
    '已发布': 'success',
    '已废止': 'danger'
  }
  return statusMap[status] || ''
}

const handleSearch = () => {
  currentPage.value = 1
  ElMessage.success('查询完成')
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'publishDateRange' ? null : ''
  })
  currentPage.value = 1
}

const handleExport = () => {
  ElMessage.success('导出功能')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const viewDetail = (row: any) => {
  selectedCSR.value = row
  showDetailDialog.value = true
}

const viewVersions = (row: any) => {
  selectedCSRId.value = row.csrId
  showVersionDialog.value = true
}

const viewApprovalHistory = (row: any) => {
  selectedCSRId.value = row.csrId
  showApprovalDialog.value = true
}

onMounted(() => {
  totalCount.value = allData.value.length
})
</script>

<style scoped>
.query-trace {
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.advanced-search {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-results {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.card-view {
  min-height: 400px;
}

.csr-card {
  margin-bottom: 20px;
  height: 280px;
}

.csr-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.csr-id {
  font-weight: bold;
  color: #409EFF;
}

.csr-card-content {
  height: 140px;
  overflow: hidden;
}

.csr-card-content h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
}

.csr-card-content p {
  margin: 6px 0;
  color: #606266;
  font-size: 12px;
}

.csr-card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>
