<template>
  <div class="version-history">
    <div class="version-list">
      <el-table :data="versionData" style="width: 100%" stripe>
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column prop="updateDate" label="更新日期" width="150" />
        <el-table-column prop="updater" label="更新人" width="120" />
        <el-table-column prop="updateReason" label="更新原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewVersion(scope.row)">查看</el-button>
              <el-button size="small" type="info" @click="compareVersion(scope.row)">对比</el-button>
              <el-button 
                size="small" 
                type="warning" 
                @click="restoreVersion(scope.row)"
                v-if="scope.row.status !== '当前版本'"
              >
                恢复
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 版本对比对话框 -->
    <el-dialog v-model="showCompareDialog" title="版本对比" width="1200px">
      <div class="version-compare">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>版本 {{ compareVersions.old?.version }} ({{ compareVersions.old?.updateDate }})</h4>
            <div class="version-content old-version">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="客户名称">{{ compareVersions.old?.customerName }}</el-descriptions-item>
                <el-descriptions-item label="文件名称">{{ compareVersions.old?.fileName }}</el-descriptions-item>
                <el-descriptions-item label="要求内容">{{ compareVersions.old?.requirementContent }}</el-descriptions-item>
                <el-descriptions-item label="控制方法">{{ compareVersions.old?.controlMethod }}</el-descriptions-item>
                <el-descriptions-item label="负责部门">{{ compareVersions.old?.department }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
          <el-col :span="12">
            <h4>版本 {{ compareVersions.new?.version }} ({{ compareVersions.new?.updateDate }})</h4>
            <div class="version-content new-version">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="客户名称">{{ compareVersions.new?.customerName }}</el-descriptions-item>
                <el-descriptions-item label="文件名称">{{ compareVersions.new?.fileName }}</el-descriptions-item>
                <el-descriptions-item label="要求内容">{{ compareVersions.new?.requirementContent }}</el-descriptions-item>
                <el-descriptions-item label="控制方法">{{ compareVersions.new?.controlMethod }}</el-descriptions-item>
                <el-descriptions-item label="负责部门">{{ compareVersions.new?.department }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps<{
  csrId: string
}>()

// 响应式数据
const showCompareDialog = ref(false)
const compareVersions = reactive({
  old: null,
  new: null
})

// 示例版本数据
const versionData = ref([
  {
    id: 1,
    version: 'V2.1',
    updateDate: '2024-01-15',
    updater: '张三',
    updateReason: '根据客户最新要求更新质量标准',
    status: '当前版本',
    customerName: '一汽大众',
    fileName: '一汽大众采购通则V2.1',
    requirementContent: '供应商必须通过IATF16949认证，产品质量必须符合VDA6.3标准，零部件PPM要求小于30...',
    controlMethod: '建立供应商评审流程，定期审核认证状态，实施月度质量评估',
    department: '品管部'
  },
  {
    id: 2,
    version: 'V2.0',
    updateDate: '2023-12-10',
    updater: '李四',
    updateReason: '增加环保要求条款',
    status: '历史版本',
    customerName: '一汽大众',
    fileName: '一汽大众采购通则V2.0',
    requirementContent: '供应商必须通过IATF16949认证，产品质量必须符合VDA6.3标准，零部件PPM要求小于50...',
    controlMethod: '建立供应商评审流程，定期审核认证状态',
    department: '品管部'
  },
  {
    id: 3,
    version: 'V1.5',
    updateDate: '2023-09-20',
    updater: '王五',
    updateReason: '修正技术规范描述',
    status: '历史版本',
    customerName: '一汽大众',
    fileName: '一汽大众采购通则V1.5',
    requirementContent: '供应商必须通过IATF16949认证，产品质量必须符合VDA6.3标准...',
    controlMethod: '建立供应商评审流程',
    department: '品管部'
  },
  {
    id: 4,
    version: 'V1.0',
    updateDate: '2023-06-01',
    updater: '赵六',
    updateReason: '初始版本',
    status: '历史版本',
    customerName: '一汽大众',
    fileName: '一汽大众采购通则V1.0',
    requirementContent: '供应商必须通过IATF16949认证...',
    controlMethod: '建立供应商评审流程',
    department: '品管部'
  }
])

// 方法
const getStatusType = (status: string) => {
  const statusMap = {
    '当前版本': 'success',
    '历史版本': 'info',
    '已废止': 'danger'
  }
  return statusMap[status] || ''
}

const viewVersion = (row: any) => {
  ElMessage.info(`查看版本 ${row.version} 详情`)
}

const compareVersion = (row: any) => {
  // 找到当前版本进行对比
  const currentVersion = versionData.value.find(v => v.status === '当前版本')
  if (currentVersion && currentVersion.id !== row.id) {
    compareVersions.old = row
    compareVersions.new = currentVersion
    showCompareDialog.value = true
  } else {
    ElMessage.warning('请选择不同的版本进行对比')
  }
}

const restoreVersion = (row: any) => {
  ElMessageBox.confirm(
    `确认恢复到版本 ${row.version} 吗？恢复后当前版本将变为历史版本。`,
    '确认恢复',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 更新版本状态
    const currentIndex = versionData.value.findIndex(v => v.status === '当前版本')
    const restoreIndex = versionData.value.findIndex(v => v.id === row.id)
    
    if (currentIndex !== -1) {
      versionData.value[currentIndex].status = '历史版本'
    }
    if (restoreIndex !== -1) {
      versionData.value[restoreIndex].status = '当前版本'
    }
    
    ElMessage.success('版本恢复成功')
  })
}

onMounted(() => {
  // 根据csrId加载版本数据
  console.log('Loading version history for CSR:', props.csrId)
})
</script>

<style scoped>
.version-history {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.version-compare {
  max-height: 600px;
  overflow-y: auto;
}

.version-compare h4 {
  margin: 0 0 16px 0;
  color: #303133;
  text-align: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.version-content {
  border-radius: 4px;
  padding: 16px;
}

.old-version {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.new-version {
  background: #f0f9ff;
  border: 1px solid #c4e1ff;
}
</style>
