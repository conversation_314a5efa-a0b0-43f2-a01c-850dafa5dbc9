<template>
  <div class="approval-list">
    <el-table :data="data" style="width: 100%" stripe>
      <el-table-column prop="csrId" label="CSR编号" width="120" />
      <el-table-column prop="customerName" label="客户名称" width="150" />
      <el-table-column prop="fileName" label="文件名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="department" label="负责部门" width="120" />
      <el-table-column prop="submitter" label="提交人" width="100" />
      <el-table-column prop="submitTime" label="提交时间" width="150" />
      
      <!-- 待审批和待会签显示优先级 -->
      <el-table-column v-if="type === 'pending' || type === 'countersign'" prop="priority" label="优先级" width="100">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)">
            {{ getPriorityText(scope.row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <!-- 会签中显示审批人信息 -->
      <el-table-column v-if="type === 'countersign'" prop="approver" label="审批人" width="120" />
      <el-table-column v-if="type === 'countersign'" prop="approveTime" label="审批时间" width="150" />
      
      <!-- 我发起的显示进度 -->
      <el-table-column v-if="type === 'initiated'" prop="currentStep" label="当前环节" width="150" />
      <el-table-column v-if="type === 'initiated'" prop="progress" label="进度" width="120">
        <template #default="scope">
          <el-progress :percentage="scope.row.progress" :stroke-width="8" />
        </template>
      </el-table-column>
      
      <!-- 历史记录显示结果 -->
      <el-table-column v-if="type === 'history'" prop="result" label="处理结果" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.result === '同意' ? 'success' : 'danger'">
            {{ scope.row.result }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="type === 'history'" prop="approveTime" label="处理时间" width="150" />
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <el-button size="small" @click="handleView(scope.row)">查看</el-button>
            
            <!-- 待审批操作 -->
            <template v-if="type === 'approval'">
              <el-button size="small" type="success" @click="handleApprove(scope.row)">
                同意
              </el-button>
              <el-button size="small" type="danger" @click="handleReject(scope.row)">
                驳回
              </el-button>
            </template>
            
            <!-- 待会签操作 -->
            <template v-if="type === 'countersign'">
              <el-button size="small" type="primary" @click="handleCountersign(scope.row)">
                会签
              </el-button>
            </template>
            
            <!-- 我发起的操作 -->
            <template v-if="type === 'initiated'">
              <el-button size="small" type="info" @click="handleTrack(scope.row)">
                流程追踪
              </el-button>
              <el-button 
                size="small" 
                type="warning" 
                @click="handleWithdraw(scope.row)"
                v-if="scope.row.status !== '已发布'"
              >
                撤回
              </el-button>
            </template>
            
            <!-- 历史记录操作 -->
            <template v-if="type === 'history'">
              <el-button size="small" type="info" @click="handleViewOpinion(scope.row)">
                查看意见
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps<{
  data: any[]
  type: 'approval' | 'countersign' | 'initiated' | 'history'
  showPagination?: boolean
  total?: number
}>()

// Emits
const emit = defineEmits<{
  approve: [row: any]
  reject: [row: any]
  view: [row: any]
  track: [row: any]
  countersign: [row: any]
}>()

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const getPriorityType = (priority: string) => {
  const typeMap = {
    'high': 'danger',
    'normal': '',
    'low': 'info'
  }
  return typeMap[priority] || ''
}

const getPriorityText = (priority: string) => {
  const textMap = {
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return textMap[priority] || priority
}

const getStatusType = (status: string) => {
  const statusMap = {
    '草稿': '',
    '待审批': 'warning',
    '审批中': 'warning',
    '会签中': 'info',
    '已发布': 'success',
    '已废止': 'danger'
  }
  return statusMap[status] || ''
}

const handleView = (row: any) => {
  emit('view', row)
}

const handleApprove = (row: any) => {
  emit('approve', row)
}

const handleReject = (row: any) => {
  emit('reject', row)
}

const handleCountersign = (row: any) => {
  emit('countersign', row)
}

const handleTrack = (row: any) => {
  emit('track', row)
}

const handleWithdraw = (row: any) => {
  ElMessageBox.confirm('确认撤回该CSR吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('撤回成功')
  })
}

const handleViewOpinion = (row: any) => {
  ElMessage.info('查看审批意见功能')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}
</script>

<style scoped>
.approval-list {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}
</style>
