<template>
  <div class="csr-detail">
    <!-- 基本信息 -->
    <div class="detail-section">
      <h3>基本信息</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="CSR编号">{{ csrData?.csrId }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(csrData?.status)">{{ csrData?.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ csrData?.customerName }}</el-descriptions-item>
        <el-descriptions-item label="文件名称">{{ csrData?.fileName }}</el-descriptions-item>
        <el-descriptions-item label="文件编号">{{ csrData?.fileNumber }}</el-descriptions-item>
        <el-descriptions-item label="版本">{{ csrData?.version }}</el-descriptions-item>
        <el-descriptions-item label="发行人">{{ csrData?.publisher }}</el-descriptions-item>
        <el-descriptions-item label="发布日期">{{ csrData?.publishDate }}</el-descriptions-item>
        <el-descriptions-item label="负责部门">{{ csrData?.department }}</el-descriptions-item>
        <el-descriptions-item label="对应过程/条款">{{ csrData?.processClause }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 要求内容 -->
    <div class="detail-section">
      <h3>要求内容</h3>
      <div class="content-box">
        {{ csrData?.requirementContent }}
      </div>
    </div>

    <!-- 控制方法 -->
    <div class="detail-section">
      <h3>控制方法</h3>
      <div class="content-box">
        {{ csrData?.controlMethod }}
      </div>
    </div>

    <!-- 相关附件 -->
    <div class="detail-section" v-if="attachments.length > 0">
      <h3>相关附件</h3>
      <div class="attachments-list">
        <el-card 
          v-for="attachment in attachments" 
          :key="attachment.id"
          class="attachment-card"
          shadow="hover"
        >
          <div class="attachment-info">
            <div class="attachment-icon">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="attachment-details">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-meta">
                <span>{{ attachment.size }}</span>
                <span>{{ attachment.uploadTime }}</span>
              </div>
            </div>
            <div class="attachment-actions">
              <el-button size="small" type="primary" @click="downloadAttachment(attachment)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              <el-button size="small" @click="previewAttachment(attachment)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 审批流程状态 -->
    <div class="detail-section">
      <h3>审批流程状态</h3>
      <div class="process-status">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step 
            v-for="(step, index) in processSteps" 
            :key="index"
            :title="step.title" 
            :description="step.description"
            :status="step.status"
          />
        </el-steps>
      </div>
    </div>

    <!-- 最近操作记录 -->
    <div class="detail-section">
      <h3>最近操作记录</h3>
      <el-table :data="recentOperations" style="width: 100%" size="small">
        <el-table-column prop="operation" label="操作" width="120" />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="operateTime" label="操作时间" width="150" />
        <el-table-column prop="description" label="操作描述" min-width="200" />
        <el-table-column prop="result" label="结果" width="80">
          <template #default="scope">
            <el-tag :type="getOperationResultType(scope.row.result)" size="small">
              {{ scope.row.result }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 操作按钮 -->
    <div class="detail-actions">
      <el-button @click="handleEdit" v-if="canEdit">
        <el-icon><Edit /></el-icon>
        编辑
      </el-button>
      <el-button type="info" @click="handleViewHistory">
        <el-icon><Clock /></el-icon>
        查看历史
      </el-button>
      <el-button type="warning" @click="handleViewApproval">
        <el-icon><List /></el-icon>
        审批记录
      </el-button>
      <el-button type="success" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出PDF
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Download, 
  View, 
  Edit, 
  Clock, 
  List 
} from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  csrData: any
}>()

// Emits
const emit = defineEmits<{
  edit: [data: any]
  viewHistory: [csrId: string]
  viewApproval: [csrId: string]
}>()

// 示例附件数据
const attachments = ref([
  {
    id: 1,
    name: '一汽大众采购通则.pdf',
    size: '2.5MB',
    uploadTime: '2024-01-10 09:00:00',
    type: 'pdf'
  },
  {
    id: 2,
    name: '技术规范说明.docx',
    size: '1.2MB',
    uploadTime: '2024-01-10 09:05:00',
    type: 'docx'
  },
  {
    id: 3,
    name: '质量要求清单.xlsx',
    size: '856KB',
    uploadTime: '2024-01-10 09:10:00',
    type: 'xlsx'
  }
])

// 流程步骤
const processSteps = ref([
  {
    title: '提交申请',
    description: '2024-01-10 09:00:00',
    status: 'finish'
  },
  {
    title: '部门审批',
    description: '2024-01-11 10:15:00',
    status: 'finish'
  },
  {
    title: '多部门会签',
    description: '2024-01-13 11:30:00',
    status: 'finish'
  },
  {
    title: '发布生效',
    description: '2024-01-15 08:00:00',
    status: 'finish'
  }
])

const currentStep = ref(4)

// 最近操作记录
const recentOperations = ref([
  {
    operation: '发布',
    operator: '系统',
    operateTime: '2024-01-15 08:00:00',
    description: 'CSR正式发布生效',
    result: '成功'
  },
  {
    operation: '会签',
    operator: '吴九',
    operateTime: '2024-01-13 11:30:00',
    description: '财务部会签通过',
    result: '通过'
  },
  {
    operation: '会签',
    operator: '周八',
    operateTime: '2024-01-12 14:10:00',
    description: '采购部会签通过',
    result: '通过'
  },
  {
    operation: '会签',
    operator: '孙七',
    operateTime: '2024-01-12 09:45:00',
    description: '生产部会签通过',
    result: '通过'
  },
  {
    operation: '审批',
    operator: '王五',
    operateTime: '2024-01-11 10:15:00',
    description: '技术总监审批通过',
    result: '通过'
  }
])

// 计算属性
const canEdit = computed(() => {
  return props.csrData?.status === '草稿' || props.csrData?.status === '已驳回'
})

// 方法
const getStatusType = (status: string) => {
  const statusMap = {
    '草稿': '',
    '待审批': 'warning',
    '审批中': 'warning',
    '会签中': 'info',
    '已发布': 'success',
    '已废止': 'danger'
  }
  return statusMap[status] || ''
}

const getOperationResultType = (result: string) => {
  const typeMap = {
    '成功': 'success',
    '通过': 'success',
    '驳回': 'danger',
    '失败': 'danger'
  }
  return typeMap[result] || ''
}

const downloadAttachment = (attachment: any) => {
  ElMessage.success(`下载附件：${attachment.name}`)
}

const previewAttachment = (attachment: any) => {
  ElMessage.info(`预览附件：${attachment.name}`)
}

const handleEdit = () => {
  emit('edit', props.csrData)
}

const handleViewHistory = () => {
  emit('viewHistory', props.csrData?.csrId)
}

const handleViewApproval = () => {
  emit('viewApproval', props.csrData?.csrId)
}

const handleExport = () => {
  ElMessage.success('导出PDF功能')
}

onMounted(() => {
  // 根据CSR状态设置流程步骤状态
  if (props.csrData?.status === '已发布') {
    currentStep.value = 4
  } else if (props.csrData?.status === '会签中') {
    currentStep.value = 2
  } else if (props.csrData?.status === '审批中') {
    currentStep.value = 1
  } else {
    currentStep.value = 0
  }
})
</script>

<style scoped>
.csr-detail {
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.content-box {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.attachments-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.attachment-card {
  cursor: pointer;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.attachment-icon {
  color: #409EFF;
}

.attachment-details {
  flex: 1;
}

.attachment-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.attachment-meta {
  font-size: 12px;
  color: #909399;
}

.attachment-meta span {
  margin-right: 12px;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.process-status {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #e6e6e6;
  margin-top: 20px;
}
</style>
